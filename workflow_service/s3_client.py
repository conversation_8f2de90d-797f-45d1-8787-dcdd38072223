import boto3
from workflow_service.config import Settings
import asyncio
settings = Settings()

s3_client = boto3.client('s3', aws_access_key_id=settings.AWS_ACCESS_KEY_ID, aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY, region_name=settings.AWS_REGION_NAME)

async def upload_file_to_s3(file_path: str, bucket: str, object_name: str) -> str:
    """从本地文件路径上传"""
    loop = asyncio.get_event_loop()
    await loop.run_in_executor(None, lambda: s3_client.upload_file(file_path, bucket, object_name))
    return f"https://cdn.roasmax.cn/{object_name}"

async def upload_bytes_to_s3(file_bytes: bytes, bucket: str, object_name: str) -> str:
    """直接从内存中的bytes上传 (新函数)"""
    loop = asyncio.get_event_loop()
    await loop.run_in_executor(None, lambda: s3_client.put_object(Body=file_bytes, Bucket=bucket, Key=object_name))
    return f"https://cdn.roasmax.cn/{object_name}"