from dotenv import dotenv_values
from pydantic_settings import BaseSettings, SettingsConfigDict

class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file='./.env', env_file_encoding='utf-8')
    COMFYUI_URL: str = "ws://127.0.0.1:8188/ws"
    COMFYUI_HTTP_URL: str = "http://127.0.0.1:8188"
    COMFYUI_INPUT_DIR: str
    COMFYUI_OUTPUT_DIR: str
    S3_BUCKET_NAME: str
    AWS_ACCESS_KEY_ID: str
    AWS_SECRET_ACCESS_KEY: str
    AWS_REGION_NAME: str

# settings = Settings()