import modal
from dotenv import dotenv_values
from workflow_service.main import web_app

fastapi_image = (
    modal.Image
    .debian_slim(python_version="3.11")
    .pip_install_from_requirements("./workflow_service/requirements.txt")
    .env(dotenv_values(".env"))
    .add_local_python_source("workflow_service")
)

app = modal.App(image=fastapi_image, name="waas-demo")
vol = modal.Volume.from_name("comfy_model", environment_name="dev", create_if_missing=True)

with fastapi_image.imports():

    @app.function(
        cpu=(0.125, 8),
        memory=(128, 4096),
        scaledown_window=1200,
        volumes={
            "/db": vol
        }
    )
    @modal.concurrent(max_inputs=100)
    @modal.asgi_app()
    def fastapi_webapp():
        return web_app