# ComfyUI 工作流管理
![这是图片](https://cdn.roasmax.cn/static/publisher.png)

## 功能
- 工作流上传
- 工作流版本控制
- 工作流加载
- 需配置工作流服务器 (详见**WAAS(工作流即服务) Demo API服务器**)


# WAAS(工作流即服务) Demo API服务器
- 路径: ./workflow_service
- 必须按照指定规则命名
  - **输入节点名**: 前缀 **INPUT_**
  - **除生成文件节点外输出节点名**: 前缀 **OUTPUT_**
- 支持输入节点: 
  - comfyui-core
    - 加载图像
  - ComfyUI-VideoHelperSuite
    - Load Video(Upload)
  - comfyui-easy-use
    - 整数
    - 字符串
    - 浮点数
- 支持输出节点: 
  - 所有在output文件夹中生成文件(图片/视频)的节点
  - comfyui-easy-use
    - 展示任何
- 数据库
  - 类型: Sqlite (workflows_service.sqlite)
- 数据库结构
  ```
  CREATE TABLE IF NOT EXISTS workflows (
      name TEXT PRIMARY KEY,
      base_name TEXT NOT NULL,
      version TEXT NOT NULL,
      workflow_json TEXT NOT NULL
  )
  ```
- 路由
  - GET /api/workflow: 列出工作流
  - POST /api/workflow: 添加工作流
  - DELETE /api/workflow: 删除工作流
  - GET /api/run/{base_name}: 获取工作流输入输出元数据
  ```
  输入:
  *base_name: 工作流名称
  version: 工作流版本
    
  输出:
  Json
  {
    "inputs": {
      "image_image": {
        "node_id": "13",
        "type": "UploadFile",
        "widget_name": "image"
      },
      "prefix_value": {
        "node_id": "22",
        "type": "int",
        "widget_name": "value"
      }
    },
    "outputs": {
      "text_output": {
        "node_id": "21",
        "class_type": "easy showAnything",
        "output_name": "output",
        "output_index": 0
      }
    }
  }
  ```
  - POST /api/run/{base_name}: 执行工作流
  ```
  输入:
  *base_name: 工作流名称
  version: 工作流版本
  
  输出:
  Json
  {
      "output_files": [
        "https://cdn.roasmax.cn/outputs/测试/4e91e429-c848-4f66-885c-98a83c745872_111_00001_.png"
      ],
      "text_output": [
        "output\\111_00001_.png"
      ]
  }
  ```